from typing import List, Optional
from dataclasses import dataclass


@dataclass
class Subtitle:
    """Subtitle data structure"""
    start_time: str
    end_time: str
    text: str


@dataclass
class SubtitleStyle:
    """Subtitle style configuration"""
    font_family: str
    font_size: int
    primary_color: str
    primary_stroke_width: int
    primary_margin_v: int
    secondary_background_color: str
    secondary_color: str
    secondary_font_family: str
    secondary_font_size: int
    secondary_stroke_color: str
    secondary_stroke_width: int
    secondary_margin_v: int
    shadow_color: str
    show_primary_shadow: bool
    show_primary_stroke: bool
    show_secondary_background: bool
    show_secondary_shadow: bool
    show_secondary_stroke: bool


@dataclass
class VideoFormat:
    """Video format configuration"""
    width: Optional[int] = None
    height: Optional[int] = None


def convert_time_to_ass(time: str) -> str:
    """
    Convert time format from "00:00:00.000" to "0:00:00.00" (ASS format)
    
    Args:
        time: Time string in format "00:00:00.000"
        
    Returns:
        Time string in ASS format "0:00:00.00"
    """
    # Remove the last millisecond digit
    time_without_last_ms = time[:-1]
    # Remove leading zero if present
    if time_without_last_ms.startswith('0'):
        return time_without_last_ms[1:]
    return time_without_last_ms


def convert_color_to_ass(color: str) -> str:
    """
    Convert color from "#RRGGBB" or "#RRGGBBAA" to ASS format "&HAABBGGRR"
    
    Args:
        color: Color string in hex format "#RRGGBB" or "#RRGGBBAA"
        
    Returns:
        Color string in ASS format "&HAABBGGRR"
    """
    # Remove # symbol
    hex_color = color.replace("#", "")
    # If 6 digits, add FF as alpha
    rgba = f"FF{hex_color}" if len(hex_color) == 6 else hex_color
    # Extract components
    aa = rgba[0:2]
    rr = rgba[2:4]
    gg = rgba[4:6]
    bb = rgba[6:8]
    # Return ASS format
    return f"&H{aa}{bb}{gg}{rr}"


def generate_ass_styles(style: SubtitleStyle, is_secondary: bool = False) -> str:
    """
    Generate ASS style definition

    Args:
        style: SubtitleStyle configuration
        is_secondary: Whether to generate secondary style

    Returns:
        ASS style definition string
    """
    font_family = style.secondary_font_family if is_secondary else style.font_family
    font_size = style.secondary_font_size if is_secondary else style.font_size
    primary_color = convert_color_to_ass(style.primary_color)
    secondary_color = convert_color_to_ass(style.secondary_color)
    outline_color = convert_color_to_ass(style.secondary_stroke_color)
    shadow_color = convert_color_to_ass(style.shadow_color)
    outline_width = style.secondary_stroke_width if is_secondary else style.primary_stroke_width
    show_outline = style.show_secondary_stroke if is_secondary else style.show_primary_stroke
    show_shadow = style.show_secondary_shadow if is_secondary else style.show_primary_shadow
    margin_v = style.secondary_margin_v if is_secondary else style.primary_margin_v

    style_name = "Secondary" if is_secondary else "Default"
    outline_value = outline_width if show_outline else 0
    shadow_value = 1 if show_shadow else 0

    # ASS Style format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour,
    # Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow,
    # Alignment, MarginL, MarginR, MarginV, Encoding
    return (f"Style: {style_name},{font_family},{font_size},{primary_color},"
            f"{secondary_color},{outline_color},{shadow_color},0,0,0,0,100,100,0,0,"
            f"{outline_value},{shadow_value},1,2,0,5,{margin_v},1,0,0")


def generate_bilingual_ass(
    src_subtitles: List[Subtitle],
    trans_subtitles: List[Subtitle],
    style: SubtitleStyle,
    format_config: Optional[VideoFormat] = None
) -> str:
    """
    Generate bilingual ASS format subtitles
    
    Args:
        src_subtitles: List of source language subtitles
        trans_subtitles: List of translated subtitles
        style: SubtitleStyle configuration
        format_config: Optional VideoFormat configuration
        
    Returns:
        Complete ASS subtitle file content as string
    """
    # Set default video dimensions if not provided
    width = format_config.width if format_config and format_config.width else 1920
    height = format_config.height if format_config and format_config.height else 1080
    
    # Generate header
    header = f"""[Script Info]
ScriptType: v4.00+
PlayResX: {width}
PlayResY: {height}
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
{generate_ass_styles(style)}
{generate_ass_styles(style, True)}

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text"""
    
    # Generate events
    events = []
    for index, src_sub in enumerate(src_subtitles):
        trans_sub = trans_subtitles[index] if index < len(trans_subtitles) else None
        start = convert_time_to_ass(src_sub.start_time)
        end = convert_time_to_ass(src_sub.end_time)

        # Source language subtitle on top (using primary marginV)
        src_line = f"Dialogue: 0,{start},{end},Default,,0,0,{style.primary_margin_v},,{src_sub.text}"

        # Translation subtitle below (using secondary marginV)
        trans_line = ""
        if trans_sub:
            trans_line = f"Dialogue: 0,{start},{end},Secondary,,0,0,{style.secondary_margin_v},,{trans_sub.text}"

        # Combine lines, filtering out empty ones
        lines = [line for line in [src_line, trans_line] if line]
        events.append("\n".join(lines))
    
    events_text = "\n".join(events)
    
    return f"{header}\n\n{events_text}"


# Example usage
if __name__ == "__main__":
    # Example subtitle data
    src_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Hello world"),
        Subtitle("00:00:04.000", "00:00:06.000", "How are you?")
    ]
    
    trans_subs = [
        Subtitle("00:00:01.000", "00:00:03.000", "Hola mundo"),
        Subtitle("00:00:04.000", "00:00:06.000", "¿Cómo estás?")
    ]
    
    # Example style configuration
    style = SubtitleStyle(
        font_family="Arial",
        font_size=20,
        primary_color="#FFFFFF",
        primary_stroke_width=2,
        primary_margin_v=40,  # Source subtitle vertical margin
        secondary_background_color="#000000",
        secondary_color="#FFFF00",
        secondary_font_family="Arial",
        secondary_font_size=18,
        secondary_stroke_color="#000000",
        secondary_stroke_width=1,
        secondary_margin_v=0,  # Translation subtitle vertical margin
        shadow_color="#000000",
        show_primary_shadow=True,
        show_primary_stroke=True,
        show_secondary_background=False,
        show_secondary_shadow=True,
        show_secondary_stroke=True
    )
    
    # Generate bilingual ASS
    ass_content = generate_bilingual_ass(src_subs, trans_subs, style)
    print(ass_content)
